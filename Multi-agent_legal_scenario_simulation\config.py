"""
CAMEL框架配置文件
配置魔塔社区API、模型参数、对话轮数等基础设置
"""

from camel.models import ModelFactory
from camel.types import ModelPlatformType, ModelType
from camel.configs import ModelScopeConfig

# 魔塔社区API配置
MODELSCOPE_BASE_URL = 'https://api-inference.modelscope.cn/v1/'
MODELSCOPE_API_KEY = '22100acc-a9e0-41fa-a9c3-a4ec69809e25'
MODELSCOPE_MODEL = 'deepseek-ai/DeepSeek-R1-0528'

# 对话轮数配置
MAX_CONVERSATION_TURNS = {
    'KQ': 15,  # 知识问答场景
    'LC': 15,  # 法律咨询场景
    'CD': 10,  # 合同起草场景
    'DD': 10,  # 文档起草场景
    'CI': 20,  # 民事诉讼场景
    'CR': 25,  # 刑事诉讼场景
}

# 单次输出token限制
MAX_TOKENS = 8192

# 测试时处理的case数量
TEST_CASE_LIMIT = 2

def create_modelscope_model():
    """
    创建魔塔社区模型实例
    """
    model_config = ModelScopeConfig(
        max_tokens=MAX_TOKENS,
        temperature=0.7
    )

    model = ModelFactory.create(
        model_platform=ModelPlatformType.MODELSCOPE,
        model_type=MODELSCOPE_MODEL,
        model_config_dict=model_config.as_dict(),
        api_key=MODELSCOPE_API_KEY,
        url=MODELSCOPE_BASE_URL
    )

    return model

def get_scenario_config(scenario_name):
    """
    获取特定场景的配置
    """
    return {
        'max_conversation_turns': MAX_CONVERSATION_TURNS.get(scenario_name, 15),
        'max_tokens': MAX_TOKENS,
        'test_case_limit': TEST_CASE_LIMIT
    }

# 结果保存路径配置
RESULT_BASE_PATH = "Multi-agent_legal_scenario_simulation/result"

# 数据文件路径配置
DATA_BASE_PATH = "Multi-agent_legal_scenario_simulation/src/data/case"

# 场景数据文件映射
SCENARIO_DATA_FILES = {
    'KQ': f"{DATA_BASE_PATH}/J1-Eval_KQ.jsonl",
    'LC': f"{DATA_BASE_PATH}/J1-Eval_LC.jsonl", 
    'CD': f"{DATA_BASE_PATH}/J1-Eval_CD.jsonl",
    'DD': f"{DATA_BASE_PATH}/J1-Eval_DD.jsonl",
    'CI': f"{DATA_BASE_PATH}/J1-Eval_CI.jsonl",
    'CR': f"{DATA_BASE_PATH}/J1-Eval_CR.jsonl"
}

# 场景描述
SCENARIO_DESCRIPTIONS = {
    'KQ': '知识问答场景 - 普通公众向法律实习生咨询法律知识',
    'LC': '法律咨询场景 - 普通公众向法律实习生进行法律咨询',
    'CD': '合同起草场景 - 特定角色与律师进行合同起草',
    'DD': '文档起草场景 - 特定角色与律师进行文档起草',
    'CI': '民事诉讼场景 - 原告、被告、法官三方进行民事诉讼',
    'CR': '刑事诉讼场景 - 被告、律师、检察官、法官四方进行刑事诉讼'
}
